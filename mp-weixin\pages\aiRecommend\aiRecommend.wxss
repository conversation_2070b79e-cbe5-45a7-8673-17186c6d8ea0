/* AI推荐页面样式 */
.ai-recommend-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: calc(60rpx + env(safe-area-inset-top));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 100;
  min-height: 120rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.nav-placeholder {
  width: 60rpx;
}

/* 主要内容 */
.content {
  padding: 20rpx 30rpx;
  padding-bottom: 100rpx;
  margin-top: 20rpx;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.input-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.input-container {
  position: relative;
  margin-bottom: 40rpx;
}

.input-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  font-size: 30rpx;
  line-height: 1.6;
  box-sizing: border-box;
}

.input-textarea:focus {
  border-color: #667eea;
}

.input-counter {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: #999;
}

/* 快捷标签 */
.quick-tags {
  margin-bottom: 40rpx;
}

.tags-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.tag-item.selected {
  background: #667eea;
  color: white;
}

/* 推荐按钮 */
.recommend-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background: #ccc;
  border-radius: 44rpx;
  transition: all 0.3s;
}

.recommend-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

/* 加载状态 */
.loading-section {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: loading-fade-in 0.3s ease-out;
}

.loading-card {
  padding: 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.loading-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.ai-thinking-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  animation: thinking-bounce 2s ease-in-out infinite;
}

.loading-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.loading-content {
  margin-bottom: 32rpx;
}

.loading-dots-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  height: 40rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  background: #667eea;
  border-radius: 50%;
  margin: 0 6rpx;
  opacity: 0.3;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.loading-subtitle {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.loading-progress {
  margin-top: 24rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
  animation: progress-loading 2s ease-in-out infinite;
}

/* 推荐结果 */
.result-section {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx 24rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.result-header-left {
  display: flex;
  align-items: center;
}

.result-header-right {
  display: flex;
  align-items: center;
}

.result-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.2);
}

.ai-avatar {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx;
  box-sizing: border-box;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.new-search-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.new-search-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.refresh-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.new-search-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 菜品列表 */
.dish-list {
  padding: 0 40rpx 20rpx 40rpx;
}

.dish-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 200rpx;
}

.dish-item:last-child {
  border-bottom: none;
}

.dish-image-container {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
  align-self: center;
}

.dish-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  object-fit: cover;
  background-color: #f5f5f5;
}

.recommend-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff6b6b;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  white-space: nowrap;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
  overflow: hidden;
}

.dish-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommend-reason {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.reason-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.reason-text {
  font-size: 24rpx;
  color: #667eea;
  flex: 1;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.dish-price {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-right: 2rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.dish-actions {
  margin-left: 16rpx;
}

.add-to-cart {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #667eea;
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  white-space: nowrap;
  transition: all 0.3s;
}

.add-to-cart:active {
  background: #5a6fd8;
  transform: scale(0.95);
}

.cart-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 重新推荐 */
.retry-section {
  padding: 32rpx 40rpx 40rpx 40rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.retry-btn {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.retry-btn:active {
  background: linear-gradient(135deg, #e8e8e8 0%, #d5d5d5 100%);
  transform: scale(0.95);
}

.retry-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

/* 空状态 */
.empty-section {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

/* 小屏幕优化样式 - 微信小程序不支持@media，这些样式作为备用 */
.dish-item-small {
  padding: 20rpx 0;
}

.dish-image-small {
  width: 140rpx;
  height: 140rpx;
}

.dish-name-small {
  font-size: 30rpx;
}

.dish-description-small {
  font-size: 24rpx;
}

.reason-text-small {
  font-size: 22rpx;
}

.price-value-small {
  font-size: 32rpx;
}

.add-to-cart-small {
  padding: 10rpx 16rpx;
  font-size: 22rpx;
}

/* 确保文本不会溢出 */
.dish-name,
.dish-description,
.reason-text {
  word-wrap: break-word;
  word-break: break-all;
}

/* 统一间距 */
.dish-list .dish-item:first-child {
  padding-top: 32rpx;
}

.dish-list .dish-item:last-child {
  padding-bottom: 0;
}

/* 加载动画效果 - 使用微信小程序支持的简单动画 */
.loading-fade-in {
  animation: fade-in 0.3s ease-out;
}

.thinking-bounce {
  animation: bounce 2s ease-in-out infinite;
}

.progress-loading {
  animation: progress 2s ease-in-out infinite;
}

/* 简单的CSS动画 - 微信小程序支持 */
.loading-dot.active {
  opacity: 1;
  transform: scale(1.2);
  background: #5a6fd8;
}

/* 渐变动画效果 */
.progress-fill {
  width: 0%;
  animation: progress-width 3s ease-in-out infinite;
}

/* 菜品详情弹窗样式 */
.pop_mask {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.4);
}

.dish_detail_pop {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}

.dish_detail_pop .div_big_image {
  width: 100%;
  height: 320rpx;
  border-radius: 10rpx;
}

.dish_detail_pop .title {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.dish_detail_pop .desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.dish_detail_pop .dish_items {
  height: 60vh;
}

.dish_detail_pop .dish_item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
}

.dish_detail_pop .dish_item .div_big_image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.dish_detail_pop .but_item {
  display: flex;
  position: relative;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  margin-top: 30rpx;
}

.dish_detail_pop .but_item .price {
  color: #e94e3c;
  font-size: 48rpx;
  font-weight: bold;
}

.dish_detail_pop .but_item .price .ico {
  font-size: 28rpx;
}

.dish_detail_pop .but_item .active {
  display: flex;
  align-items: center;
}

.dish_detail_pop .but_item .active .dish_add,
.dish_detail_pop .but_item .active .dish_red {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.dish_detail_pop .but_item .active .dish_number {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.dish_detail_pop .but_item .active .dish_card_add {
  width: 200rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  background: #ffc200;
  border-radius: 30rpx;
  color: #333;
}

.dish_detail_pop .close {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  transform: translateX(-50%);
}

.dish_detail_pop .close .close_img {
  width: 88rpx;
  height: 88rpx;
}

/* 规格选择弹窗样式 */
.more_norm_pop {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  padding: 40rpx;
  transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}

.more_norm_pop .title {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.more_norm_pop .items_cont {
  display: flex;
  flex-wrap: wrap;
  margin-left: -14rpx;
  max-height: 50vh;
}

.more_norm_pop .items_cont .item_row {
  width: 100%;
  margin-bottom: 30rpx;
}

.more_norm_pop .items_cont .item_row .flavor_name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.more_norm_pop .items_cont .item_row .flavor_item {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.more_norm_pop .items_cont .item_row .flavor_item .item {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  transition: all 0.3s;
}

.more_norm_pop .items_cont .item_row .flavor_item .item.act {
  background: #ffc200;
  border: 1px solid #ffc200;
  color: #333;
  font-weight: 500;
}

.more_norm_pop .but_item {
  display: flex;
  position: relative;
  flex: 1;
  padding-left: 10rpx;
  margin: 34rpx 0 -20rpx 0;
  align-items: center;
  justify-content: space-between;
}

.more_norm_pop .but_item .price {
  text-align: left;
  color: #e94e3c;
  line-height: 88rpx;
  box-sizing: border-box;
  font-size: 48rpx;
  font-weight: 500;
}

.more_norm_pop .but_item .price .ico {
  font-size: 28rpx;
}

.more_norm_pop .but_item .active {
  position: absolute;
  right: 0rpx;
  bottom: 20rpx;
  display: flex;
}

.more_norm_pop .but_item .active .dish_card_add {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  opacity: 1;
  background: #ffc200;
  border-radius: 30rpx;
  color: #333;
}

.more_norm_pop .close {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  transform: translateX(-50%);
}

.more_norm_pop .close .close_img {
  width: 88rpx;
  height: 88rpx;
}
