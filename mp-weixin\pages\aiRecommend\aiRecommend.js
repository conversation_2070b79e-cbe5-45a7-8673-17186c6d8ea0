// AI智能推荐页面
const { getApiUrl } = require('../../utils/config.js');
const { querySetmealDishById, addToShoppingCart, subShoppingCart, userLogin } = require('../../api/api.js');

Page({
  data: {
    userInput: '', // 用户输入的需求
    isLoading: false, // 是否正在加载
    hasSearched: false, // 是否已经搜索过
    hasSelectedTags: false, // 是否选择了标签
    loadingDotIndex: 0, // 当前激活的加载点
    quickTags: [
      { id: 1, name: '清淡口味', selected: false },
      { id: 2, name: '麻辣口味', selected: false },
      { id: 3, name: '素食主义', selected: false },
      { id: 4, name: '低卡健康', selected: false },
      { id: 5, name: '下饭菜', selected: false },
      { id: 6, name: '汤品类', selected: false },
      { id: 7, name: '老人适宜', selected: false },
      { id: 8, name: '儿童喜爱', selected: false }
    ],
    recommendResult: [], // 推荐结果
    // 菜品详情弹窗相关数据
    openDetailPop: false, // 是否显示菜品详情弹窗
    dishDetailes: {}, // 菜品详情数据
    dishMealData: [], // 套餐菜品数据
    // 规格选择弹窗相关数据
    openMoreNormPop: false, // 是否显示规格选择弹窗
    moreNormDishdata: {}, // 规格选择菜品数据
    moreNormdata: [], // 规格数据
    flavorDataes: [] // 已选择的规格数据
  },

  onLoad: function(options) {
    // 页面加载时的初始化
    console.log('AI推荐页面加载');

    // 检查用户登录状态
    this.checkUserLogin();

    // 尝试恢复之前的推荐结果
    this.restoreRecommendResult();
  },

  onUnload: function() {
    // 页面卸载时保存当前状态
    if (this.data.recommendResult.length > 0) {
      this.saveRecommendResult(this.data.recommendResult);
    }
  },

  onHide: function() {
    // 页面隐藏时保存当前状态
    if (this.data.recommendResult.length > 0) {
      this.saveRecommendResult(this.data.recommendResult);
    }
  },

  // 恢复推荐结果
  restoreRecommendResult: function() {
    try {
      const savedResult = wx.getStorageSync('aiRecommendResult');
      const savedInput = wx.getStorageSync('aiRecommendInput');
      const savedTags = wx.getStorageSync('aiRecommendTags');

      if (savedResult && savedResult.length > 0) {
        console.log('恢复之前的推荐结果:', savedResult);

        // 恢复推荐结果
        this.setData({
          recommendResult: savedResult,
          userInput: savedInput || '',
          hasSearched: true
        });

        // 恢复选中的标签
        if (savedTags && savedTags.length > 0) {
          const updatedTags = this.data.quickTags.map(tag => ({
            ...tag,
            selected: savedTags.includes(tag.name)
          }));

          this.setData({
            quickTags: updatedTags,
            hasSelectedTags: savedTags.length > 0
          });
        }
      }
    } catch (e) {
      console.error('恢复推荐结果失败:', e);
    }
  },

  // 保存推荐结果到本地存储
  saveRecommendResult: function(recommendations) {
    try {
      const { userInput, quickTags } = this.data;
      const selectedTags = quickTags.filter(tag => tag.selected).map(tag => tag.name);

      // 保存推荐结果、用户输入和选中的标签
      wx.setStorageSync('aiRecommendResult', recommendations);
      wx.setStorageSync('aiRecommendInput', userInput);
      wx.setStorageSync('aiRecommendTags', selectedTags);

      console.log('推荐结果已保存到本地存储');
    } catch (e) {
      console.error('保存推荐结果失败:', e);
    }
  },

  // 清除保存的推荐结果
  clearSavedRecommendResult: function() {
    try {
      wx.removeStorageSync('aiRecommendResult');
      wx.removeStorageSync('aiRecommendInput');
      wx.removeStorageSync('aiRecommendTags');
      console.log('已清除保存的推荐结果');
    } catch (e) {
      console.error('清除推荐结果失败:', e);
    }
  },

  // 检查是否应该清除推荐结果
  checkIfShouldClearResults: function() {
    // 防抖处理，避免频繁弹窗
    if (this.clearResultsTimer) {
      clearTimeout(this.clearResultsTimer);
    }

    this.clearResultsTimer = setTimeout(() => {
      wx.showModal({
        title: '提示',
        content: '您修改了搜索条件，是否要清除当前推荐结果？',
        confirmText: '清除',
        cancelText: '保留',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              recommendResult: [],
              hasSearched: false
            });
            this.clearSavedRecommendResult();
          }
        }
      });
    }, 1000); // 1秒后显示提示
  },

  // 检查用户登录状态
  checkUserLogin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      // 如果没有token，进行微信登录
      this.doWechatLogin();
    } else {
      console.log('用户已登录，token:', token);
    }
  },

  // 微信登录
  doWechatLogin: function() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('获取微信授权码成功:', res.code);

          // 调用后端登录接口
          userLogin(res.code).then((loginRes) => {
            if (loginRes.code === 1) {
              // 登录成功，保存token
              wx.setStorageSync('token', loginRes.data.token);
              wx.setStorageSync('userInfo', loginRes.data);
              console.log('用户登录成功:', loginRes.data);

              // 清除之前用户的推荐结果
              this.clearSavedRecommendResult();

              wx.showToast({
                title: '登录成功',
                icon: 'success'
              });
            } else {
              console.error('登录失败:', loginRes.msg);
              wx.showToast({
                title: loginRes.msg || '登录失败',
                icon: 'error'
              });
            }
          }).catch((err) => {
            console.error('登录请求失败:', err);
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'error'
            });
          });
        } else {
          console.error('获取微信授权码失败:', res.errMsg);
          wx.showToast({
            title: '获取授权失败',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('微信登录失败:', err);
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        });
      }
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 输入框内容变化
  onInputChange: function(e) {
    const newValue = e.detail.value;
    const oldValue = this.data.userInput;

    this.setData({
      userInput: newValue
    });

    // 如果有推荐结果且输入发生了实质性变化，提示用户
    if (this.data.recommendResult.length > 0 && newValue !== oldValue && newValue.length > 0) {
      this.checkIfShouldClearResults();
    }
  },

  // 选择快捷标签
  selectTag: function(e) {
    const index = e.currentTarget.dataset.index;
    const quickTags = this.data.quickTags;
    const wasSelected = quickTags[index].selected;
    quickTags[index].selected = !quickTags[index].selected;

    // 检查是否有选中的标签
    const hasSelectedTags = quickTags.some(tag => tag.selected);

    this.setData({
      quickTags: quickTags,
      hasSelectedTags: hasSelectedTags
    });

    // 如果有推荐结果且标签选择发生了变化，提示用户
    if (this.data.recommendResult.length > 0 && wasSelected !== quickTags[index].selected) {
      this.checkIfShouldClearResults();
    }
  },

  // 获取AI推荐
  getRecommendation: function() {
    if (this.data.userInput.length === 0 && !this.data.hasSelectedTags) {
      wx.showToast({
        title: '请输入需求或选择标签',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      hasSearched: true,
      recommendResult: [] // 清空当前结果
    });

    // 启动加载动画
    this.startLoadingAnimation();

    // 调用后端AI推荐API
    this.callBackendAI();
  },

  // 重新搜索（清除保存的结果）
  newSearch: function() {
    // 清除保存的推荐结果
    this.clearSavedRecommendResult();

    // 重置页面状态
    this.setData({
      userInput: '',
      recommendResult: [],
      hasSearched: false,
      hasSelectedTags: false,
      quickTags: this.data.quickTags.map(tag => ({ ...tag, selected: false }))
    });

    wx.showToast({
      title: '已清除搜索结果',
      icon: 'success'
    });
  },

  // 调用后端AI推荐API
  callBackendAI: function() {
    const { userInput, quickTags } = this.data;
    const selectedTags = quickTags.filter(tag => tag.selected).map(tag => tag.name);

    // 构建请求参数
    const requestData = {
      userInput: userInput || '',
      selectedTags: selectedTags,
      limit: 5
    };

    console.log('调用后端AI推荐API，请求参数：', requestData);

    // 使用配置化的API调用方式
    wx.request({
      url: getApiUrl('aiRecommend'), // 使用配置文件中的URL
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('后端AI推荐响应：', res);
        this.handleBackendResponse(res);
      },
      fail: (err) => {
        console.error('后端AI推荐调用失败：', err);
        this.handleBackendError(err);
      }
    });
  },

  // 处理后端响应
  handleBackendResponse: function(res) {
    try {
      if (res.statusCode === 200 && res.data && res.data.code === 1) {
        const recommendations = res.data.data;
        console.log('AI推荐结果：', recommendations);

        // 转换数据格式以适配前端显示
        const formattedRecommendations = recommendations.map(item => ({
          id: item.dishId,
          name: item.name,
          description: item.description,
          price: item.price,
          image: item.image || '/static/dish1.jpg', // 默认图片
          recommendReason: item.recommendReason,
          score: item.score || 90,
          tags: [item.categoryName || '美食'], // 使用分类名作为标签
          type: item.type || 1, // 菜品类型，1为菜品，2为套餐
          flavors: item.flavors || [], // 菜品规格信息
          dishNumber: item.dishNumber || 0 // 购物车中的数量
        }));

        this.setData({
          isLoading: false,
          recommendResult: formattedRecommendations
        });

        // 保存推荐结果到本地存储，在用户本次登录期间有效
        this.saveRecommendResult(formattedRecommendations);

        // 停止加载动画
        this.stopLoadingAnimation();
      } else {
        throw new Error(res.data?.msg || '推荐服务返回异常');
      }
    } catch (error) {
      console.error('处理后端响应失败：', error);
      this.setData({
        isLoading: false
      });

      // 停止加载动画
      this.stopLoadingAnimation();

      wx.showToast({
        title: '推荐服务暂时不可用',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 处理后端调用错误
  handleBackendError: function(err) {
    console.error('后端调用失败:', err);
    this.setData({
      isLoading: false
    });

    // 停止加载动画
    this.stopLoadingAnimation();

    wx.showToast({
      title: '网络连接失败，请稍后重试',
      icon: 'none',
      duration: 2000
    });
  },



  // 打开菜品详情
  openDetailHandle: function(e) {
    const dish = e.currentTarget.dataset.dish;
    console.log('打开菜品详情:', dish);

    // 转换数据格式以适配主页的菜品详情弹窗
    const dishDetailes = {
      id: dish.id,
      name: dish.name,
      description: dish.description,
      price: dish.price,
      image: dish.image,
      type: dish.type || 1, // 默认为菜品类型
      flavors: dish.flavors || [],
      dishNumber: dish.dishNumber || 0
    };

    this.setData({
      dishDetailes: dishDetailes
    });

    // 如果是套餐类型，需要查询套餐菜品
    if (dishDetailes.type === 2) {
      querySetmealDishById({
        id: dishDetailes.id
      }).then((res) => {
        if (res.code === 1) {
          this.setData({
            openDetailPop: true,
            dishMealData: res.data
          });
        }
      }).catch((err) => {
        console.error('查询套餐菜品失败:', err);
        this.setData({
          openDetailPop: true
        });
      });
    } else {
      this.setData({
        openDetailPop: true
      });
    }
  },

  // 关闭菜品详情弹窗
  closeDetailPop: function() {
    this.setData({
      openDetailPop: false
    });
  },

  // 加入购物车 - 从推荐列表直接加入
  addToCart: function(e) {
    const dish = e.currentTarget.dataset.dish;

    // 检查菜品是否有规格，如果有规格则弹出规格选择弹窗
    if (dish.flavors && dish.flavors.length > 0) {
      this.moreNormDataesHandle(e);
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 构建购物车数据，按照后端ShoppingCartDTO格式
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    console.log('加入购物车数据:', cartData);

    // 调用后端API加入购物车
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 加入购物车 - 从详情弹窗加入
  addDishAction: function(e, type) {
    const dish = e.currentTarget.dataset.dish || this.data.dishDetailes;

    console.log('加入购物车:', dish, type);

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 构建购物车数据，按照后端ShoppingCartDTO格式
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dish.dishFlavor || null
    };

    console.log('加入购物车数据:', cartData);

    // 调用后端API加入购物车
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });

        // 更新菜品数量显示
        const updatedDish = { ...dish };
        updatedDish.dishNumber = (updatedDish.dishNumber || 0) + 1;

        this.setData({
          dishDetailes: updatedDish
        });
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 减少菜品数量
  redDishAction: function(e, type) {
    const dish = e.currentTarget.dataset.dish || this.data.dishDetailes;

    console.log('减少菜品:', dish, type);

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    if (dish.dishNumber > 0) {
      // 构建购物车数据，按照后端ShoppingCartDTO格式
      const cartData = {
        dishId: dish.type === 1 ? dish.id : null,
        setmealId: dish.type === 2 ? dish.id : null,
        dishFlavor: dish.dishFlavor || null
      };

      console.log('减少购物车数据:', cartData);

      // 调用后端API减少购物车商品
      subShoppingCart(cartData).then((res) => {
        if (res.code === 1) {
          // 更新菜品数量显示
          const updatedDish = { ...dish };
          updatedDish.dishNumber = updatedDish.dishNumber - 1;

          this.setData({
            dishDetailes: updatedDish
          });

          wx.showToast({
            title: `已从购物车移除`,
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.msg || '操作失败',
            icon: 'error'
          });
        }
      }).catch((err) => {
        console.error('减少购物车商品失败:', err);
        wx.showToast({
          title: '操作失败',
          icon: 'error'
        });
      });
    }
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: 'AI智能推荐 - 发现更适合你的美食',
      path: '/pages/aiRecommend/aiRecommend'
    };
  },

  // 启动加载动画
  startLoadingAnimation: function() {
    this.dotTimer = setInterval(() => {
      const currentIndex = this.data.loadingDotIndex;
      const nextIndex = (currentIndex + 1) % 5;
      this.setData({
        loadingDotIndex: nextIndex
      });
    }, 400);
  },

  // 停止加载动画
  stopLoadingAnimation: function() {
    if (this.dotTimer) {
      clearInterval(this.dotTimer);
      this.dotTimer = null;
    }
    this.setData({
      loadingDotIndex: 0
    });
  },

  // 打开规格选择弹窗
  moreNormDataesHandle: function(e) {
    const dish = e.currentTarget.dataset.dish;
    console.log('打开规格选择弹窗:', dish);

    // 设置规格选择菜品数据
    this.setData({
      moreNormDishdata: dish,
      moreNormdata: dish.flavors || [],
      flavorDataes: [],
      openMoreNormPop: true
    });
  },

  // 选择规格选项
  checkMoreNormPop: function(e) {
    const { flavorValue, flavorObj } = e.currentTarget.dataset;
    console.log('选择规格:', flavorValue, flavorObj);

    let flavorDataes = [...this.data.flavorDataes];
    const existingIndex = flavorDataes.findIndex(item => item === flavorValue);

    if (existingIndex !== -1) {
      // 如果已选择，则取消选择
      flavorDataes.splice(existingIndex, 1);
    } else {
      // 如果未选择，则添加选择
      flavorDataes.push(flavorValue);
    }

    this.setData({
      flavorDataes: flavorDataes
    });
  },

  // 从规格弹窗加入购物车
  addShop: function(e) {
    const dish = this.data.moreNormDishdata;
    const selectedFlavors = this.data.flavorDataes;

    // 检查是否选择了所有必需的规格
    const requiredFlavors = this.data.moreNormdata;
    if (requiredFlavors.length > 0 && selectedFlavors.length === 0) {
      wx.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      });
      this.doWechatLogin();
      return;
    }

    // 构建规格字符串
    const dishFlavor = selectedFlavors.length > 0 ? selectedFlavors.join(',') : null;

    // 构建购物车数据
    const cartData = {
      dishId: dish.type === 1 ? dish.id : null,
      setmealId: dish.type === 2 ? dish.id : null,
      dishFlavor: dishFlavor
    };

    console.log('从规格弹窗加入购物车:', cartData);

    // 调用后端API加入购物车
    addToShoppingCart(cartData).then((res) => {
      if (res.code === 1) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
        // 关闭规格选择弹窗
        this.closeMoreNorm();
      } else {
        wx.showToast({
          title: res.msg || '加入购物车失败',
          icon: 'error'
        });
      }
    }).catch((err) => {
      console.error('加入购物车失败:', err);
      wx.showToast({
        title: '加入购物车失败',
        icon: 'error'
      });
    });
  },

  // 关闭规格选择弹窗
  closeMoreNorm: function() {
    this.setData({
      openMoreNormPop: false,
      moreNormDishdata: {},
      moreNormdata: [],
      flavorDataes: []
    });
  },

  // 微信登录
  doWechatLogin: function() {
    // 这里可以添加微信登录逻辑
    console.log('执行微信登录');
  },

  // 页面卸载时清理
  onUnload: function() {
    this.stopLoadingAnimation();
  }
});
